# Netlify Google OAuth Authentication Fix

## Problem
The Google OAuth authentication was failing with the error:
```
error decoding lambda response: error decoding lambda response: j<PERSON>: cannot unmarshal object into Go struct field .body of type string
```

This happened because:
1. **Static Export + Database Sessions**: The app was configured for static export but trying to use database sessions
2. **API Routes**: Static export doesn't support API routes, which NextAuth.js requires
3. **Improper Function Handler**: The Netlify function wasn't properly handling NextAuth requests

## Solution

### 1. Switched from Static Export to Regular Next.js Build
- Removed `output: 'export'` from Next.js config
- Changed publish directory from `out` to `.next`
- This allows API routes to work properly

### 2. Changed Session Strategy from Database to JWT
- Updated NextAuth configuration to use JWT sessions instead of database sessions
- This works better with serverless functions and doesn't require database connections
- Updated callbacks to work with JWT tokens

### 3. Fixed Netlify Function Handler
- Updated `netlify/functions/auth.js` to properly handle NextAuth requests
- Added proper CORS headers and request/response handling
- Fixed path parsing for NextAuth actions

### 4. Updated Redirects
- Added specific redirect for `/api/auth/*` to the auth function
- Maintained general API redirect for other endpoints

## Files Changed

1. **next.config.js** - Removed static export configuration
2. **netlify.toml** - Updated build command and publish directory
3. **pages/api/auth/[...nextauth].js** - Switched to JWT sessions
4. **netlify/functions/auth.js** - Fixed function handler
5. **.env.netlify** - Updated environment configuration

## Required Environment Variables

Set these in your Netlify dashboard (Site settings > Environment variables):

### Essential for Authentication
```
NEXTAUTH_SECRET=your_super_strong_random_secret_here
NEXTAUTH_URL=https://ghostlayer-ai.netlify.app
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
```

### Optional (for full functionality)
```
DATABASE_URL=your_postgresql_connection_string
STRIPE_SECRET_KEY=your_stripe_secret_key
HUGGINGFACE_API_TOKEN=your_huggingface_token
```

## Google Cloud Console Configuration

Make sure your Google OAuth app is configured with:

### Authorized JavaScript origins:
- `https://ghostlayer-ai.netlify.app`

### Authorized redirect URIs:
- `https://ghostlayer-ai.netlify.app/api/auth/callback/google`

## How It Works Now

1. **Authentication Flow**:
   - User clicks "Sign in with Google"
   - Redirects to `/api/auth/signin/google`
   - Netlify redirects to `/.netlify/functions/auth/signin/google`
   - Function handles OAuth flow with Google
   - Creates JWT session (no database required)
   - Redirects back to application

2. **Session Management**:
   - Uses JWT tokens stored in HTTP-only cookies
   - No database connection required for sessions
   - User data stored in JWT payload

3. **API Routes**:
   - All `/api/auth/*` routes handled by Netlify function
   - Other API routes can be added as separate functions

## Testing

After deployment, test the authentication:

1. Visit your Netlify site
2. Click "Sign in with Google"
3. Complete OAuth flow
4. Should redirect back with successful authentication

## Troubleshooting

### If authentication still fails:

1. **Check Environment Variables**: Ensure all required variables are set in Netlify dashboard
2. **Verify Google OAuth Config**: Double-check authorized origins and redirect URIs
3. **Check Function Logs**: View Netlify function logs for detailed error messages
4. **CORS Issues**: Ensure your domain is properly configured in Google Console

### Common Issues:

- **Wrong NEXTAUTH_URL**: Must match your actual Netlify domain
- **Missing NEXTAUTH_SECRET**: Generate with `openssl rand -base64 32`
- **Google OAuth Mismatch**: Authorized URIs must exactly match your domain
- **Function Timeout**: Large dependencies might cause cold start issues

## Benefits of This Approach

- ✅ Works with Netlify serverless functions
- ✅ No database required for sessions
- ✅ Faster cold starts (no database connections)
- ✅ Proper CORS handling
- ✅ Compatible with static hosting + functions
- ✅ Scalable and cost-effective

The authentication should now work properly on Netlify!
