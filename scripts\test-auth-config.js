#!/usr/bin/env node

/**
 * Test script to verify NextAuth configuration
 */

const fs = require('fs');
const path = require('path');

function testAuthConfig() {
  console.log('🔐 Testing NextAuth configuration...');
  
  const errors = [];
  const warnings = [];

  // Check if NextAuth config file exists
  const authConfigPath = path.join(process.cwd(), 'pages/api/auth/[...nextauth].js');
  if (!fs.existsSync(authConfigPath)) {
    errors.push('NextAuth config file not found at pages/api/auth/[...nextauth].js');
  } else {
    console.log('✅ NextAuth config file found');
    
    // Read and check config
    const configContent = fs.readFileSync(authConfigPath, 'utf8');
    
    // Check for JWT strategy
    if (configContent.includes("strategy: 'jwt'")) {
      console.log('✅ JWT session strategy configured');
    } else if (configContent.includes("strategy: 'database'")) {
      warnings.push('Database session strategy detected - should use JWT for Netlify');
    }
    
    // Check for Google provider
    if (configContent.includes('GoogleProvider')) {
      console.log('✅ Google OAuth provider configured');
    } else {
      errors.push('Google OAuth provider not found');
    }
  }

  // Check Netlify function
  const functionPath = path.join(process.cwd(), 'netlify/functions/auth.js');
  if (!fs.existsSync(functionPath)) {
    errors.push('Netlify auth function not found at netlify/functions/auth.js');
  } else {
    console.log('✅ Netlify auth function found');
    
    const functionContent = fs.readFileSync(functionPath, 'utf8');
    if (functionContent.includes("strategy: 'jwt'")) {
      console.log('✅ Netlify function uses JWT strategy');
    } else {
      warnings.push('Netlify function should use JWT strategy');
    }
  }

  // Check netlify.toml
  const netlifyConfigPath = path.join(process.cwd(), 'netlify.toml');
  if (!fs.existsSync(netlifyConfigPath)) {
    errors.push('netlify.toml not found');
  } else {
    console.log('✅ netlify.toml found');
    
    const netlifyContent = fs.readFileSync(netlifyConfigPath, 'utf8');
    if (netlifyContent.includes('/api/auth/*')) {
      console.log('✅ Auth API redirects configured');
    } else {
      errors.push('Auth API redirects not configured in netlify.toml');
    }
  }

  // Check environment variables
  console.log('\n🔧 Checking environment variables...');
  const requiredEnvVars = [
    'NEXTAUTH_SECRET',
    'NEXTAUTH_URL',
    'GOOGLE_CLIENT_ID',
    'GOOGLE_CLIENT_SECRET'
  ];

  requiredEnvVars.forEach(envVar => {
    if (process.env[envVar]) {
      console.log(`✅ ${envVar} is set`);
    } else {
      warnings.push(`${envVar} environment variable not set`);
    }
  });

  // Check Next.js config
  const nextConfigPath = path.join(process.cwd(), 'next.config.js');
  if (fs.existsSync(nextConfigPath)) {
    const nextConfigContent = fs.readFileSync(nextConfigPath, 'utf8');
    if (nextConfigContent.includes("output: 'export'")) {
      errors.push('Next.js config uses static export - this breaks API routes');
    } else {
      console.log('✅ Next.js config allows API routes');
    }
  }

  // Summary
  console.log('\n📊 Configuration Summary:');
  
  if (errors.length === 0) {
    console.log('✅ No critical errors found');
  } else {
    console.log('❌ Critical errors found:');
    errors.forEach(error => console.log(`  - ${error}`));
  }

  if (warnings.length === 0) {
    console.log('✅ No warnings');
  } else {
    console.log('⚠️ Warnings:');
    warnings.forEach(warning => console.log(`  - ${warning}`));
  }

  console.log('\n🚀 Next steps:');
  console.log('1. Set required environment variables in Netlify dashboard');
  console.log('2. Configure Google OAuth in Google Cloud Console');
  console.log('3. Deploy to Netlify and test authentication');

  return errors.length === 0;
}

// Run the test
if (testAuthConfig()) {
  console.log('\n🎉 Configuration looks good!');
  process.exit(0);
} else {
  console.log('\n💥 Configuration has critical errors that need to be fixed');
  process.exit(1);
}
