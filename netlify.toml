[build]
  # Build command for Next.js with API routes - <PERSON>p prepare scripts to avoid husky issues
  command = "npm ci --include=dev --ignore-scripts && npm run postinstall && npm run build"

  # Let Netlify handle Next.js automatically - no publish directory needed
  # Netlify's Next.js plugin will handle API routes and functions

  # Environment variables for build
  environment = { NODE_VERSION = "18", NEXT_TELEMETRY_DISABLED = "1", NETLIFY = "true", HUSKY = "0" }

[build.processing]
  # Skip processing of certain file types for faster builds
  skip_processing = false

[build.processing.css]
  # Enable CSS optimization
  bundle = true
  minify = true

[build.processing.js]
  # Enable JavaScript optimization
  bundle = true
  minify = true

[build.processing.html]
  # Enable HTML optimization
  pretty_urls = true

[[redirects]]
  from = "/home"
  to = "/"
  status = 301

[[redirects]]
  from = "/health"
  to = "/api/health"
  status = 200

# Removed API redirects to functions since we're using Next.js API routes
# NextAuth.js will work directly through Next.js API routes

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "camera=(), microphone=(), geolocation=()"

[[headers]]
  for = "/api/*"
  [headers.values]
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Methods = "GET, POST, PUT, DELETE, OPTIONS"
    Access-Control-Allow-Headers = "Content-Type, Authorization"
    Cache-Control = "no-store, max-age=0"

# Specific headers for HTML files
[[headers]]
  for = "/*.html"
  [headers.values]
    Cache-Control = "public, max-age=0, must-revalidate"

# Performance headers for static assets
[[headers]]
  for = "/static/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Functions not needed - Next.js API routes will be handled automatically by Netlify

# No plugins needed for static export

# Environment-specific configurations
[context.production]
  command = "npm ci --include=dev --ignore-scripts && npm run postinstall && npm run build"

  [context.production.environment]
    NODE_ENV = "production"
    NETLIFY = "true"
    HUSKY = "0"
    NEXT_PUBLIC_SITE_URL = "https://ghostlayer-ai.netlify.app"

[context.deploy-preview]
  command = "npm ci --include=dev --ignore-scripts && npm run postinstall && npm run build"

  [context.deploy-preview.environment]
    NODE_ENV = "production"
    NETLIFY = "true"
    HUSKY = "0"
    NEXT_PUBLIC_SITE_URL = "https://deploy-preview-$REVIEW_ID--ghostlayer-ai.netlify.app"

[context.branch-deploy]
  command = "npm ci --include=dev --ignore-scripts && npm run postinstall && npm run build"

  [context.branch-deploy.environment]
    NODE_ENV = "production"
    NETLIFY = "true"
    HUSKY = "0"
    NEXT_PUBLIC_SITE_URL = "https://$BRANCH--ghostlayer-ai.netlify.app"

# Plugin configurations
[[plugins]]
  package = "@netlify/plugin-nextjs"

[[plugins]]
  package = "netlify-plugin-submit-sitemap"

  [plugins.inputs]
    baseUrl = "https://ghostlayer-ai.netlify.app"
    sitemapPath = "/sitemap.xml"
    ignorePeriod = 0
    providers = [
      "google",
      "bing",
      "yandex"
    ]

[dev]
  command = "npm run dev"
  port = 3000
  publish = "out"
