# 🔧 Netlify Build Error Fix

## Problem
Build failed with error: `Your publish directory was not found at: /opt/build/repo/out`

## Root Cause
When using `@netlify/plugin-nextjs`, you should **NOT** specify a publish directory. The plugin handles the build output automatically, but your Netlify site settings still have `out` configured as the publish directory.

## ✅ Solution

### Step 1: Clear Publish Directory in Netlify UI
1. Go to your Netlify site dashboard
2. Navigate to **Site settings** → **Build & deploy** → **Build settings**
3. **Clear the "Publish directory" field** (leave it completely empty)
4. Click **Save**

### Step 2: Verify netlify.toml Configuration
The `netlify.toml` should NOT have a `publish` setting in the `[build]` section when using the Next.js plugin:

```toml
[build]
  command = "npm ci --include=dev --ignore-scripts && npm run postinstall && npm run build"
  # NO publish directory - @netlify/plugin-nextjs handles this

[[plugins]]
  package = "@netlify/plugin-nextjs"
```

### Step 3: Trigger New Deploy
After clearing the publish directory, trigger a new deploy.

## 🔍 Why This Happens
- `@netlify/plugin-nextjs` automatically detects and handles Next.js build output
- When you specify a publish directory, it conflicts with the plugin's automatic detection
- The plugin expects to manage the entire deployment process

## ✅ Expected Result
After fixing:
- Build should complete successfully
- Next.js API routes (including `/api/auth/*`) will work properly
- OAuth authentication should function correctly

## 🚨 If Still Failing
If the build still fails after clearing the publish directory:

1. Check that `@netlify/plugin-nextjs` is in your `package.json` devDependencies
2. Verify the plugin is listed in `netlify.toml` under `[[plugins]]`
3. Try deploying from a fresh commit

The key is: **No publish directory + @netlify/plugin-nextjs = Success!**
