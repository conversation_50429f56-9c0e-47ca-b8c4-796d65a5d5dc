import NextAuth from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
import { PrismaClient } from '@prisma/client';
import { PrismaAdapter } from '@next-auth/prisma-adapter';

// Instantiate PrismaClient. It's good practice to do this globally,
// rather than in every request, to manage connection pooling efficiently.
const prisma = new PrismaClient();

export const authOptions = {
    // Use JWT sessions for Netlify compatibility (no database required)
    // adapter: PrismaAdapter(prisma), // Commented out for Netlify deployment

    providers: [
        GoogleProvider({
            clientId: process.env.GOOGLE_CLIENT_ID,
            clientSecret: process.env.GOOGLE_CLIENT_SECRET,
            authorization: { // Recommended for Google to ensure refresh tokens are available
                params: {
                    prompt: "consent",
                    access_type: "offline",
                    response_type: "code"
                }
            }
        }),
        // ...add more providers here if needed
    ],

    secret: process.env.NEXTAUTH_SECRET,

    // Session strategy changed to 'jwt' for Netlify serverless compatibility
    session: {
        strategy: 'jwt',
        maxAge: 30 * 24 * 60 * 60, // 30 days
        updateAge: 24 * 60 * 60, // 24 hours
    },

    callbacks: {
        // JWT callback for storing user data in the token (JWT strategy)
        async jwt({ token, user, account }) {
            // Store OAuth access token if available
            if (account?.access_token) {
                token.accessToken = account.access_token;
            }

            // Add user data to token on first sign in
            if (user) {
                token.subscriptionTier = 'free'; // Default subscription tier
                token.usageCredits = 10; // Default usage credits
                // You can add more user data here as needed
            }

            return token;
        },

        // Session callback for adding token data to the session (JWT strategy)
        async session({ session, token }) {
            // Add user data from token to session
            if (session?.user && token) {
                session.user.id = token.sub; // Standard JWT subject claim
                session.user.subscriptionTier = token.subscriptionTier || 'free';
                session.user.usageCredits = token.usageCredits || 10;

                // Add access token if needed for API calls
                if (token.accessToken) {
                    session.accessToken = token.accessToken;
                }
            }

            return session;
        }
    },

    // Optional: Custom pages for NextAuth.js actions.
    // pages: {
    //   signIn: '/auth/signin',
    //   signOut: '/auth/signout',
    //   error: '/auth/error',
    // },

    debug: process.env.NODE_ENV === 'development',
};

export default NextAuth(authOptions);
