# 🔧 Google OAuth Fix for Netlify Deployment

## Problem Diagnosed
The error `error decoding lambda response: json: cannot unmarshal object into Go struct field .body of type string` was caused by:

1. **Conflicting Auth Handlers**: Both Next.js API routes and Netlify functions were configured
2. **Incorrect Redirects**: API routes were being redirected to functions unnecessarily
3. **Missing Next.js Plugin**: Netlify wasn't properly handling Next.js API routes

## ✅ Fixes Applied

### 1. Removed Conflicting Redirects
- Removed `/api/auth/*` to `/.netlify/functions/auth/:splat` redirect
- Removed `/api/*` to `/.netlify/functions/:splat` redirect
- Next.js API routes will now work directly

### 2. Added Netlify Next.js Plugin
- Installed `@netlify/plugin-nextjs`
- Added plugin configuration to `netlify.toml`
- This enables proper Next.js support on Netlify

### 3. Updated Netlify Configuration
- Removed manual functions directory configuration
- Let Netlify handle Next.js automatically
- Simplified build configuration

### 4. Fixed Response Handling
- Updated Netlify function (if still needed) to properly handle response format
- Ensured response body is always a string for Netlify compatibility

## 🔍 Required Environment Variables

Make sure these are set in your Netlify dashboard:

```
NEXTAUTH_SECRET=your_super_strong_random_secret_here
NEXTAUTH_URL=https://ghostlayer-ai.netlify.app
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
```

## 🌐 Google Cloud Console Configuration

Ensure your Google OAuth app has these settings:

### Authorized JavaScript Origins:
- `https://ghostlayer-ai.netlify.app`

### Authorized Redirect URIs:
- `https://ghostlayer-ai.netlify.app/api/auth/callback/google`

## 🚀 Next Steps

1. **Deploy to Netlify** - The configuration should now work
2. **Test OAuth Flow** - Try signing in with Google
3. **Check Logs** - Monitor Netlify function logs for any remaining issues

## 🔧 If Issues Persist

If you still get errors, check:

1. **Environment Variables**: Verify all 4 required variables are set in Netlify
2. **Google Console**: Ensure redirect URI matches exactly
3. **Build Logs**: Check Netlify build logs for any deployment issues
4. **Function Logs**: Monitor Netlify function logs during OAuth attempts

## 📝 Key Changes Made

- `netlify.toml`: Removed API redirects, added Next.js plugin
- `package.json`: Added `@netlify/plugin-nextjs` dependency
- Configuration now uses Next.js API routes directly instead of custom functions

The OAuth flow should now work properly with the standard Next.js API route handling!
